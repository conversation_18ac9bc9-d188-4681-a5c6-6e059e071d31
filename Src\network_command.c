/**
  ******************************************************************************
  * @file    network_command.c
  * @brief   网络下发指令解析和处理模块实现
  ******************************************************************************
  */

#include "network_command.h"
#include "FLASH/bsp_flash.h"
#include "rtc_sync.h"
#include "rtc.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/**
 * @brief  解析网络下发指令
 * @param  command: 指令字符串
 * @param  result: 解析结果结构体指针
 * @retval HAL_StatusTypeDef 解析状态
 */
HAL_StatusTypeDef NetworkCommand_Parse(const char* command, NetworkCommand_Result_t* result)
{
    if (command == NULL || result == NULL) {
        return HAL_ERROR;
    }

    // 初始化结果结构体
    memset(result, 0, sizeof(NetworkCommand_Result_t));
    result->type = CMD_TYPE_UNKNOWN;
    result->is_valid = 0;

    // 检查指令前缀 "ZL+"
    if (strncmp(command, "ZL+", 3) != 0) {
        printf("Invalid command prefix: %s\r\n", command);
        return HAL_ERROR;
    }

    // 获取指令类型字符
    char cmd_char = command[3];
    const char* value_str = &command[4];

    switch (cmd_char) {
        case 'D':  // 休眠天数
            result->type = CMD_TYPE_SLEEP_DAYS;
            result->value1 = (uint32_t)atoi(value_str);
            if (result->value1 > 0 && result->value1 <= 365) {  // 限制1-365天
                result->is_valid = 1;
                printf("Sleep days command: %lu days\r\n", result->value1);
            } else {
                printf("Invalid sleep days value: %lu\r\n", result->value1);
            }
            break;

        case 'H':  // 休眠小时数
            result->type = CMD_TYPE_SLEEP_HOURS;
            result->value1 = (uint32_t)atoi(value_str);
            if (result->value1 > 0 && result->value1 <= 8760) {  // 限制1-8760小时（1年）
                result->is_valid = 1;
                printf("Sleep hours command: %lu hours\r\n", result->value1);
            } else {
                printf("Invalid sleep hours value: %lu\r\n", result->value1);
            }
            break;

        case 'M':  // 休眠分钟数
            result->type = CMD_TYPE_SLEEP_MINUTES;
            result->value1 = (uint32_t)atoi(value_str);
            if (result->value1 > 0 && result->value1 <= 525600) {  // 限制1-525600分钟（1年）
                result->is_valid = 1;
                printf("Sleep minutes command: %lu minutes\r\n", result->value1);
            } else {
                printf("Invalid sleep minutes value: %lu\r\n", result->value1);
            }
            break;

        case 'S':  // 休眠秒数
            result->type = CMD_TYPE_SLEEP_SECONDS;
            result->value1 = (uint32_t)atoi(value_str);
            if (result->value1 > 0 && result->value1 <= 31536000) {  // 限制1-31536000秒（1年）
                result->is_valid = 1;
                printf("Sleep seconds command: %lu seconds\r\n", result->value1);
            } else {
                printf("Invalid sleep seconds value: %lu\r\n", result->value1);
            }
            break;

        case 'F':  // 工作时间段设置
            {
                // 查找E字符的位置
                char* e_pos = strchr(value_str, 'E');
                if (e_pos != NULL) {
                    // 提取开始时间
                    char start_str[8] = {0};
                    uint16_t start_len = e_pos - value_str;
                    if (start_len < sizeof(start_str)) {
                        strncpy(start_str, value_str, start_len);
                        result->value1 = (uint32_t)atoi(start_str);

                        // 提取结束时间
                        result->value2 = (uint32_t)atoi(e_pos + 1);

                        // 验证时间范围（0-23小时）
                        if (result->value1 <= 23 && result->value2 <= 23) {
                            result->type = CMD_TYPE_WORK_TIME;
                            result->is_valid = 1;
                            printf("Work time command: %lu:00 - %lu:00\r\n", result->value1, result->value2);
                        } else {
                            printf("Invalid work time range: %lu - %lu\r\n", result->value1, result->value2);
                        }
                    }
                } else {
                    printf("Invalid work time format, missing 'E': %s\r\n", command);
                }
            }
            break;

        case 'N':  // 打包数据条数（暂时不用）
            result->type = CMD_TYPE_PACK_COUNT;
            result->value1 = (uint32_t)atoi(value_str);
            printf("Pack count command (not implemented): %lu\r\n", result->value1);
            break;

        case 'T':  // 发送设备信息（暂时不用）
            result->type = CMD_TYPE_SEND_INFO;
            printf("Send info command (not implemented)\r\n");
            break;

        default:
            printf("Unknown command type: %c\r\n", cmd_char);
            return HAL_ERROR;
    }

    return result->is_valid ? HAL_OK : HAL_ERROR;
}

/**
 * @brief  将解析结果保存到FLASH
 * @param  result: 解析结果结构体指针
 * @retval HAL_StatusTypeDef 保存状态
 */
HAL_StatusTypeDef NetworkCommand_SaveToFlash(const NetworkCommand_Result_t* result)
{
    if (result == NULL || !result->is_valid) {
        return HAL_ERROR;
    }

    HAL_StatusTypeDef status = HAL_OK;

    switch (result->type) {
        case CMD_TYPE_SLEEP_DAYS:
        case CMD_TYPE_SLEEP_HOURS:
        case CMD_TYPE_SLEEP_MINUTES:
        case CMD_TYPE_SLEEP_SECONDS:
            // 休眠时间设置使用覆盖机制，都存储在同一个索引位置
            // 同时保存指令类型和数值
            status = Flash_WriteUint32(FLASH_INDEX_SLEEP_TIME,
                                     (uint32_t)result->type | (result->value1 << 8));
            if (status == HAL_OK) {
                printf("Sleep time saved to FLASH: type=%d, value=%lu\r\n",
                       result->type, result->value1);
            }
            break;

        case CMD_TYPE_WORK_TIME:
            // 工作时间段分别保存开始和结束时间
            status = Flash_WriteUint32(FLASH_INDEX_WORK_TIME_START, result->value1);
            if (status == HAL_OK) {
                status = Flash_WriteUint32(FLASH_INDEX_WORK_TIME_END, result->value2);
            }
            if (status == HAL_OK) {
                printf("Work time saved to FLASH: %lu:00 - %lu:00\r\n",
                       result->value1, result->value2);
            }
            break;

        case CMD_TYPE_PACK_COUNT:
            // 暂时不实现
            printf("Pack count save not implemented\r\n");
            break;

        case CMD_TYPE_SEND_INFO:
            // 暂时不实现
            printf("Send info save not implemented\r\n");
            break;

        default:
            return HAL_ERROR;
    }

    return status;
}

/**
 * @brief  从FLASH加载指令设置
 * @param  type: 指令类型
 * @param  result: 结果结构体指针
 * @retval HAL_StatusTypeDef 加载状态
 */
HAL_StatusTypeDef NetworkCommand_LoadFromFlash(NetworkCommand_Type_t type, NetworkCommand_Result_t* result)
{
    if (result == NULL) {
        return HAL_ERROR;
    }

    memset(result, 0, sizeof(NetworkCommand_Result_t));
    HAL_StatusTypeDef status = HAL_OK;

    switch (type) {
        case CMD_TYPE_SLEEP_DAYS:
        case CMD_TYPE_SLEEP_HOURS:
        case CMD_TYPE_SLEEP_MINUTES:
        case CMD_TYPE_SLEEP_SECONDS:
            {
                uint32_t data = 0;
                status = Flash_ReadUint32(FLASH_INDEX_SLEEP_TIME, &data);
                if (status == HAL_OK) {
                    result->type = (NetworkCommand_Type_t)(data & 0xFF);
                    result->value1 = data >> 8;
                    result->is_valid = 1;
                }
            }
            break;

        case CMD_TYPE_WORK_TIME:
            status = Flash_ReadUint32(FLASH_INDEX_WORK_TIME_START, &result->value1);
            if (status == HAL_OK) {
                status = Flash_ReadUint32(FLASH_INDEX_WORK_TIME_END, &result->value2);
                if (status == HAL_OK) {
                    result->type = CMD_TYPE_WORK_TIME;
                    result->is_valid = 1;
                }
            }
            break;

        default:
            return HAL_ERROR;
    }

    return status;
}

/**
 * @brief  处理网络指令（解析并保存）
 * @param  command: 指令字符串
 * @retval HAL_StatusTypeDef 处理状态
 */
HAL_StatusTypeDef NetworkCommand_Process(const char* command)
{
    NetworkCommand_Result_t result;
    HAL_StatusTypeDef status;

    // 解析指令
    status = NetworkCommand_Parse(command, &result);
    if (status != HAL_OK) {
        printf("Command parse failed: %s\r\n", command);
        return status;
    }

    // 保存到FLASH
    status = NetworkCommand_SaveToFlash(&result);
    if (status != HAL_OK) {
        printf("Command save to FLASH failed\r\n");
        return status;
    }

    // 如果是休眠时间指令，立即更新设备休眠参数
    if (result.type == CMD_TYPE_SLEEP_DAYS ||
        result.type == CMD_TYPE_SLEEP_HOURS ||
        result.type == CMD_TYPE_SLEEP_MINUTES ||
        result.type == CMD_TYPE_SLEEP_SECONDS) {

        status = NetworkCommand_UpdateSleepTime(result.type, result.value1);
        if (status != HAL_OK) {
            printf("Sleep time update failed\r\n");
            return status;
        }
    }

    return HAL_OK;
}

/**
 * @brief  检查当前是否在工作时间段内
 * @retval uint8_t 0=不在工作时间段/未设置, 1=在工作时间段内, 2=RTC未同步, 4=工作时间段被禁用
 */
uint8_t NetworkCommand_CheckWorkTime(void)
{
    NetworkCommand_Result_t result;

    // 检查RTC时间有效性标志
    extern uint8_t rtc_time_valid;
    if (rtc_time_valid == 0) {
        printf("RTC time not synced (default time), work time check disabled\r\n");
        return 2;  // RTC未同步
    }

    // 使用外部全局变量中保存的唤醒时间（避免重新读取RTC可能的问题）
    extern RTC_TimeTypeDef current_wakeup_time;
    extern RTC_DateTypeDef current_wakeup_date;

    // 从FLASH加载工作时间段设置
    if (NetworkCommand_LoadFromFlash(CMD_TYPE_WORK_TIME, &result) != HAL_OK || !result.is_valid) {
        // 未设置工作时间段，返回0表示不启用工作时间段功能
        printf("Work time not configured, using default operation\r\n");
        return 0;
    }

    // 检查开始时间是否等于结束时间
    if (result.value1 == result.value2) {
        // 开始时间==结束时间，表示不启用工作时间段功能
        printf("Work time disabled (start==end: %lu:00), using default operation\r\n", result.value1);
        return 4;  // 返回4表示工作时间段被禁用
    }

    // 使用唤醒时保存的RTC时间
    RTC_TimeTypeDef current_time = current_wakeup_time;

    uint8_t current_hour = current_time.Hours;
    uint8_t start_hour = (uint8_t)result.value1;
    uint8_t end_hour = (uint8_t)result.value2;

    printf("Work time configured: %02d:00 - %02d:00, current: %02d:%02d:%02d\r\n",
           start_hour, end_hour, current_hour, current_time.Minutes, current_time.Seconds);

    // 检查是否在工作时间段内
    if (start_hour <= end_hour) {
        // 正常时间段，如 08:00 - 18:00
        if (current_hour >= start_hour && current_hour <= end_hour) {
            printf("Current time is within work hours\r\n");
            return 1;
        }
    } else {
        // 跨天时间段，如 22:00 - 06:00
        if (current_hour >= start_hour || current_hour <= end_hour) {
            printf("Current time is within work hours (cross-day)\r\n");
            return 1;
        }
    }

    // 不在工作时间段内，计算智能休眠时间
    uint32_t seconds_to_work = NetworkCommand_CalculateSecondsToWorkTime(
        start_hour, end_hour, current_hour, current_time.Minutes, current_time.Seconds);

    if (seconds_to_work > 0) {
        // 限制最大休眠时间（RTC唤醒定时器限制，约18小时）
        const uint32_t MAX_SLEEP_SECONDS = 65535; // RTC计数器最大值
        if (seconds_to_work > MAX_SLEEP_SECONDS) {
            seconds_to_work = MAX_SLEEP_SECONDS;
            printf("Sleep time limited to maximum: %lu seconds (%.1f hours)\r\n",
                   seconds_to_work, seconds_to_work / 3600.0f);
        } else {
            printf("Calculated smart sleep time: %lu seconds (%.1f hours) until work time\r\n",
                   seconds_to_work, seconds_to_work / 3600.0f);
        }

        // 更新全局休眠计数器
        extern uint32_t wakeup_counter;
        wakeup_counter = seconds_to_work - 1;
        printf("Smart sleep: wakeup_counter updated to %lu\r\n", wakeup_counter);

        return 3; // 新的返回值：已设置智能休眠时间
    }

    printf("Current time is outside work hours\r\n");
    return 0;
}

/**
 * @brief  计算当前时间到工作时间段开始的秒数
 * @param  start_hour: 工作时间段开始小时
 * @param  end_hour: 工作时间段结束小时
 * @param  current_hour: 当前小时
 * @param  current_minute: 当前分钟
 * @param  current_second: 当前秒
 * @retval uint32_t 到工作时间段的秒数，0表示已在工作时间段内
 */
uint32_t NetworkCommand_CalculateSecondsToWorkTime(uint8_t start_hour, uint8_t end_hour,
                                                   uint8_t current_hour, uint8_t current_minute, uint8_t current_second)
{
    // 当前时间的总秒数（从00:00:00开始）
    uint32_t current_seconds = current_hour * 3600 + current_minute * 60 + current_second;
    uint32_t start_seconds = start_hour * 3600;
    uint32_t end_seconds = end_hour * 3600;

    if (start_hour <= end_hour) {
        // 正常时间段，如 08:00 - 18:00
        if (current_seconds >= start_seconds && current_seconds <= end_seconds) {
            return 0; // 已在工作时间段内
        } else if (current_seconds < start_seconds) {
            // 当前时间早于工作时间段，计算到今天工作时间段开始的秒数
            return start_seconds - current_seconds;
        } else {
            // 当前时间晚于今天的工作时间段，计算到明天工作时间段开始的秒数
            return (24 * 3600) - current_seconds + start_seconds;
        }
    } else {
        // 跨天时间段，如 22:00 - 06:00
        if (current_seconds >= start_seconds || current_seconds <= end_seconds) {
            return 0; // 已在工作时间段内
        } else {
            // 不在工作时间段内，计算到下一个工作时间段开始的秒数
            if (current_seconds < start_seconds) {
                // 当前时间早于今天的工作时间段开始，计算到今天工作时间段开始的秒数
                return start_seconds - current_seconds;
            } else {
                // 当前时间在工作时间段结束后，计算到明天工作时间段开始的秒数
                return (24 * 3600) - current_seconds + start_seconds;
            }
        }
    }
}

/**
 * @brief  更新设备休眠时间参数
 * @param  type: 休眠时间类型
 * @param  value: 休眠时间数值
 * @retval HAL_StatusTypeDef 更新状态
 */
HAL_StatusTypeDef NetworkCommand_UpdateSleepTime(NetworkCommand_Type_t type, uint32_t value)
{
    // 使用freertos.c中定义的wakeup_counter变量
    extern uint32_t wakeup_counter;
    uint32_t sleep_seconds = 0;

    switch (type) {
        case CMD_TYPE_SLEEP_DAYS:
            sleep_seconds = value * 24 * 3600;  // 转换为秒
            printf("Sleep time updated: %lu days (%lu seconds)\r\n", value, sleep_seconds);
            break;

        case CMD_TYPE_SLEEP_HOURS:
            sleep_seconds = value * 3600;  // 转换为秒
            printf("Sleep time updated: %lu hours (%lu seconds)\r\n", value, sleep_seconds);
            break;

        case CMD_TYPE_SLEEP_MINUTES:
            sleep_seconds = value * 60;  // 转换为秒
            printf("Sleep time updated: %lu minutes (%lu seconds)\r\n", value, sleep_seconds);
            break;

        case CMD_TYPE_SLEEP_SECONDS:
            sleep_seconds = value;
            printf("Sleep time updated: %lu seconds\r\n", sleep_seconds);
            break;

        default:
            return HAL_ERROR;
    }

    // 立即更新wakeup_counter，使当前周期就生效
    wakeup_counter = sleep_seconds - 1;
    printf("Wakeup counter immediately updated to: %lu (effective this cycle)\r\n", wakeup_counter);

    return HAL_OK;
}

/**
 * @brief  设备启动时从FLASH加载设置
 */
void NetworkCommand_LoadSettingsOnBoot(void)
{
    extern uint32_t wakeup_counter;

    printf("Loading settings from FLASH on boot...\r\n");

    // 加载休眠时间设置
    NetworkCommand_Result_t sleep_result;
    if (NetworkCommand_LoadFromFlash(CMD_TYPE_SLEEP_DAYS, &sleep_result) == HAL_OK && sleep_result.is_valid) {
        uint32_t sleep_seconds = 30;  // 默认30秒

        switch (sleep_result.type) {
            case CMD_TYPE_SLEEP_DAYS:
                sleep_seconds = sleep_result.value1 * 24 * 3600;
                printf("Loaded sleep setting: %lu days (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                break;
            case CMD_TYPE_SLEEP_HOURS:
                sleep_seconds = sleep_result.value1 * 3600;
                printf("Loaded sleep setting: %lu hours (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                break;
            case CMD_TYPE_SLEEP_MINUTES:
                sleep_seconds = sleep_result.value1 * 60;
                printf("Loaded sleep setting: %lu minutes (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                break;
            case CMD_TYPE_SLEEP_SECONDS:
                sleep_seconds = sleep_result.value1;
                printf("Loaded sleep setting: %lu seconds\r\n", sleep_seconds);
                break;
            default:
                printf("Invalid sleep setting type, using default: 30 seconds\r\n");
                break;
        }

        wakeup_counter = sleep_seconds - 1;
        printf("Boot: wakeup_counter set to %lu\r\n", wakeup_counter);
    } else {
        printf("No sleep setting in FLASH, using default: 30 seconds\r\n");
        wakeup_counter = 30 - 1;
    }

    // 加载工作时间段设置
    NetworkCommand_Result_t work_result;
    if (NetworkCommand_LoadFromFlash(CMD_TYPE_WORK_TIME, &work_result) == HAL_OK && work_result.is_valid) {
        printf("Loaded work time setting: %lu:00 - %lu:00\r\n", work_result.value1, work_result.value2);
    } else {
        printf("No work time setting in FLASH, work time feature disabled\r\n");
    }
}

/**
 * @brief  打印解析结果
 * @param  result: 解析结果结构体指针
 */
void NetworkCommand_PrintResult(const NetworkCommand_Result_t* result)
{
    if (result == NULL) {
        printf("Result is NULL\r\n");
        return;
    }

    printf("Command Result:\r\n");
    printf("  Type: %d\r\n", result->type);
    printf("  Value1: %lu\r\n", result->value1);
    printf("  Value2: %lu\r\n", result->value2);
    printf("  Valid: %d\r\n", result->is_valid);
}
