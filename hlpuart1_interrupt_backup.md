# LPUART1串口中断接收处理逻辑备份文档

## 文档说明
- **创建时间**: 2025-01-08
- **目的**: 备份当前hlpuart1中断处理逻辑，防止修改过程中代码混乱
- **重要性**: ZL+指令识别机制经过多次调试才成功，必须保证修改不影响此功能
- **文件位置**: `Src/main.c` 中的 `HAL_UART_RxCpltCallback` 函数

## 关键变量定义

### 全局变量 (main.c)
```c
// LPUART1 (GSM/网络指令) 接收缓冲区
#define LPUART1_RX_BUFFER_SIZE 1
uint8_t lpuart1_rx_buffer[LPUART1_RX_BUFFER_SIZE];

// GSM响应缓冲区（用于中断接收）
#define GSM_INTERRUPT_BUFFER_SIZE 512
char gsm_interrupt_buffer[GSM_INTERRUPT_BUFFER_SIZE];
uint16_t gsm_interrupt_buffer_index = 0;
uint8_t gsm_response_ready = 0;

// 网络指令缓冲区
#define NETWORK_CMD_BUFFER_SIZE 32
#define NETWORK_CMD_QUEUE_SIZE 4
char network_cmd_queue[NETWORK_CMD_QUEUE_SIZE][NETWORK_CMD_BUFFER_SIZE];
uint8_t network_cmd_queue_head = 0;
uint8_t network_cmd_queue_tail = 0;
uint8_t network_cmd_queue_count = 0;

char network_cmd_saved[NETWORK_CMD_BUFFER_SIZE];
uint8_t network_cmd_new_data = 0;
```

## 当前完整中断处理代码

### HAL_UART_RxCpltCallback 函数 - LPUART1部分
```c
else if(huart->Instance == LPUART1)  // LPUART1统一中断接收
{
    uint8_t received_char = lpuart1_rx_buffer[0];

    if (gsm_interrupt_buffer_index < GSM_INTERRUPT_BUFFER_SIZE - 1) {
        gsm_interrupt_buffer[gsm_interrupt_buffer_index++] = received_char;
        gsm_interrupt_buffer[gsm_interrupt_buffer_index] = '\0';

        // 先检查 AT 指令响应结束标志（优先处理AT指令）
        uint16_t len = gsm_interrupt_buffer_index;
        if ((len >= 4 && strcmp(&gsm_interrupt_buffer[len-4], "OK\r\n") == 0) ||
            (len >= 7 && strcmp(&gsm_interrupt_buffer[len-7], "ERROR\r\n") == 0) ||
            (len >= 6 && strcmp(&gsm_interrupt_buffer[len-6], "FAIL\r\n") == 0) ||
            (len >= 12 && strcmp(&gsm_interrupt_buffer[len-12], "CONNECT OK\r\n") == 0) ||
            (len >= 9 && strcmp(&gsm_interrupt_buffer[len-9], "SEND OK\r\n") == 0) ||
            (len >= 10 && strcmp(&gsm_interrupt_buffer[len-10], "CLOSE OK\r\n") == 0) ||
            (len >= 8 && strcmp(&gsm_interrupt_buffer[len-8], "CLOSED\r\n") == 0) ||
            (len >= 8 && strcmp(&gsm_interrupt_buffer[len-8], "ACCEPT\r\n") == 0) ||
            (strstr(gsm_interrupt_buffer, "DATA ACCEPT:") != NULL && received_char == '\n'))
        {
            gsm_response_ready = 1;

            // 即使是AT响应，也要检查缓冲区中是否有ZL+指令（特别是CONNECT OK后的情况）
            // 尝试从缓冲区中提取每一条完整的ZL+网络指令
            while (1) {
                char* zl_start = strstr(gsm_interrupt_buffer, "ZL+");
                if (zl_start == NULL) break;

                // 查找指令末尾（\n）
                char* zl_end = strchr(zl_start, '\n');
                if (zl_end == NULL) break;  // 没有完整结尾，等下一轮再处理

                // 计算指令长度（含ZL+和结尾符）
                size_t cmd_len = zl_end - zl_start + 1;
                if (cmd_len >= NETWORK_CMD_BUFFER_SIZE) cmd_len = NETWORK_CMD_BUFFER_SIZE - 1;

                // 拷贝指令到保存缓冲区（注意：network_cmd_saved 是单条命令缓冲区）
                strncpy(network_cmd_saved, zl_start, cmd_len);
                network_cmd_saved[cmd_len] = '\0';

                // 移除末尾的 \r \n
                for (int i = 0; i < NETWORK_CMD_BUFFER_SIZE; i++) {
                    if (network_cmd_saved[i] == '\r' || network_cmd_saved[i] == '\n' || network_cmd_saved[i] == '\0') {
                        network_cmd_saved[i] = '\0';
                        break;
                    }
                }

                // 将指令加入队列
                if (NetworkCmd_QueuePush(network_cmd_saved)) {
                    network_cmd_new_data = 1;
                }

                // 移除已处理部分，移动剩余数据到头部
                size_t remaining_len = gsm_interrupt_buffer_index - (zl_end - gsm_interrupt_buffer + 1);
                memmove(gsm_interrupt_buffer, zl_end + 1, remaining_len);
                gsm_interrupt_buffer[remaining_len] = '\0';
                gsm_interrupt_buffer_index = remaining_len;

                // 可继续 while 循环处理下一个ZL+指令
            }
                 }
         else if (received_char == '>') {
             gsm_response_ready = 1;
         }
         else {
             // 非AT指令响应时，处理ZL+网络指令
            // 尝试从缓冲区中提取每一条完整的ZL+网络指令
            while (1) {
                char* zl_start = strstr(gsm_interrupt_buffer, "ZL+");
                if (zl_start == NULL) break;

                // 查找指令末尾（\n）
                char* zl_end = strchr(zl_start, '\n');
                if (zl_end == NULL) break;  // 没有完整结尾，等下一轮再处理

                // 计算指令长度（含ZL+和结尾符）
                size_t cmd_len = zl_end - zl_start + 1;
                if (cmd_len >= NETWORK_CMD_BUFFER_SIZE) cmd_len = NETWORK_CMD_BUFFER_SIZE - 1;

                // 拷贝指令到保存缓冲区（注意：network_cmd_saved 是单条命令缓冲区）
                strncpy(network_cmd_saved, zl_start, cmd_len);
                network_cmd_saved[cmd_len] = '\0';

                // 移除末尾的 \r \n
                for (int i = 0; i < NETWORK_CMD_BUFFER_SIZE; i++) {
                    if (network_cmd_saved[i] == '\r' || network_cmd_saved[i] == '\n' || network_cmd_saved[i] == '\0') {
                        network_cmd_saved[i] = '\0';
                        break;
                    }
                }

                // 将指令加入队列
                if (NetworkCmd_QueuePush(network_cmd_saved)) {
                    network_cmd_new_data = 1;
                }

                // 移除已处理部分，移动剩余数据到头部
                size_t remaining_len = gsm_interrupt_buffer_index - (zl_end - gsm_interrupt_buffer + 1);
                memmove(gsm_interrupt_buffer, zl_end + 1, remaining_len);
                gsm_interrupt_buffer[remaining_len] = '\0';
                gsm_interrupt_buffer_index = remaining_len;

                // 可继续 while 循环处理下一个ZL+指令
            }
        }
    } else {
        // 缓冲区满，重置
        gsm_interrupt_buffer_index = 0;
        gsm_response_ready = 0;
        memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);
    }

    // 重新启动接收中断
    HAL_UART_Receive_IT(&hlpuart1, &lpuart1_rx_buffer[0], 1);
}
```

## 处理逻辑说明

### 1. 接收字符处理
- **逐字符接收**: 每次接收1个字符到 `lpuart1_rx_buffer[0]`
- **累积存储**: 将字符累积到 `gsm_interrupt_buffer`
- **索引管理**: 使用 `gsm_interrupt_buffer_index` 管理缓冲区位置
- **空终止符**: 每次添加字符后都添加 `\0`

### 2. AT指令响应检测机制
检测以下AT指令响应结束标志：
- `"OK\r\n"`
- `"ERROR\r\n"`
- `"FAIL\r\n"`
- `"CONNECT OK\r\n"`
- `"SEND OK\r\n"`
- `"CLOSE OK\r\n"`
- `"CLOSED\r\n"`
- `"ACCEPT\r\n"`
- `"DATA ACCEPT:"` + `\n`
- `">"` (AT+CIPSEND指令的提示符)

### 3. ZL+指令检测机制（双重检测）

#### 3.1 AT响应完成后检测ZL+
```c
// 即使是AT响应，也要检查缓冲区中是否有ZL+指令
while (1) {
    char* zl_start = strstr(gsm_interrupt_buffer, "ZL+");
    if (zl_start == NULL) break;

    char* zl_end = strchr(zl_start, '\n');
    if (zl_end == NULL) break;

    // 处理ZL+指令...
}
```

#### 3.2 非AT响应时检测ZL+
```c
// 非AT指令响应时，处理ZL+网络指令
while (1) {
    char* zl_start = strstr(gsm_interrupt_buffer, "ZL+");
    // ... 相同的处理逻辑
}
```

### 4. ZL+指令处理流程
1. **查找ZL+开始位置**: `strstr(gsm_interrupt_buffer, "ZL+")`
2. **查找结束位置**: `strchr(zl_start, '\n')`
3. **计算指令长度**: `zl_end - zl_start + 1`
4. **拷贝指令**: `strncpy(network_cmd_saved, zl_start, cmd_len)`
5. **移除结束符**: 移除 `\r` 和 `\n`
6. **加入队列**: `NetworkCmd_QueuePush(network_cmd_saved)`
7. **设置标志**: `network_cmd_new_data = 1`
8. **移除已处理数据**: `memmove` 移动剩余数据到头部
9. **更新缓冲区索引**: `gsm_interrupt_buffer_index = remaining_len`

### 5. 多指令处理机制
- **循环处理**: 使用 `while (1)` 循环处理多个ZL+指令
- **队列管理**: 使用 `network_cmd_queue` 存储多条指令
- **逐条处理**: 每次处理一条完整的ZL+指令后移除，继续处理下一条

## 关键问题分析

### 🔴 当前存在的问题
1. **AT响应标志设置错误**: `gsm_response_ready = 1` 似乎不在正确的条件判断内
2. **时序问题**: 可能在接收不完整数据时就触发处理
3. **缓冲区竞争**: AT响应和ZL+指令可能存在处理冲突

### ✅ **关键发现 - ">"提示符检测**
代码中包含对`received_char == '>'`的单独检测，这是AT+CIPSEND指令的重要响应：
```c
else if (received_char == '>') {
    gsm_response_ready = 1;
}
```
这个机制确保GSM数据发送功能正常工作，**修改时必须保留此检测**！

### 🟢 ZL+指令处理的优势
1. **双重检测**: 在AT响应和非AT响应时都检测ZL+
2. **完整性检查**: 必须找到完整的ZL+....\n才处理
3. **多指令支持**: 支持一次接收多条ZL+指令
4. **队列管理**: 使用队列避免指令丢失
5. **数据移动**: 处理完一条指令后正确移动剩余数据

## 修复建议

### 保守修复方案
1. **只修复AT响应检测**: 确保 `gsm_response_ready = 1` 在正确的条件内
2. **保持ZL+检测不变**: 完全保持当前的ZL+检测逻辑
3. **增加时序控制**: 在AT指令发送后增加短暂延迟

### 代码修复位置
```c
// 当前问题代码位置：main.c 第456行附近
if ((len >= 4 && strcmp(&gsm_interrupt_buffer[len-4], "OK\r\n") == 0) ||
    // ... 其他条件
    )
{
    gsm_response_ready = 1;  // 确保这行在条件判断内

    // ZL+检测逻辑保持不变
    // ...
}
```

## 恢复代码指南

如果修改导致问题，按以下步骤恢复：

1. **恢复中断处理函数**: 使用本文档中的完整代码
2. **检查变量定义**: 确保所有全局变量定义正确
3. **验证ZL+功能**: 测试多指令ZL+处理是否正常
4. **测试AT响应**: 确保AT指令响应正常

## 测试验证

### ZL+指令测试
1. 发送单条ZL+指令
2. 发送多条ZL+指令
3. 在AT响应后发送ZL+指令
4. 验证队列处理是否正常

### AT响应测试
1. 基本AT指令（ATE0）
2. 数据指令（AT+CIPSEND）
3. 连接指令（AT+CIPSTART）
4. 验证响应检测是否正常

---

**⚠️ 重要提醒**:
- 任何修改都必须保持ZL+指令检测的双重机制
- 不要改变ZL+指令的处理流程
- **必须保留">"提示符的检测机制**（GSM数据发送关键功能）
- 修改前先备份当前工作版本
- 逐步修改并充分测试
