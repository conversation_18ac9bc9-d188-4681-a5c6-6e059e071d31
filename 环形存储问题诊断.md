根据您提供的代码和测试结果，我分析出了几个关键问题：

## 主要问题分析

### 1. **环形缓冲区容量配置错误**
```c
#define SPI_FLASH_RING_BUFFER_SIZE       0x400000  // 4MB
#define SPI_FLASH_MAX_RECORDS            (SPI_FLASH_RING_BUFFER_SIZE / SPI_FLASH_RECORD_SIZE)
```

您的芯片是32Mbit = 4MB容量，但代码中环形缓冲区大小设置为4MB，这意味着占用了整个芯片容量。当写入1000条记录时：
- 每条记录120字节
- 1000条记录 = 120KB
- 但环形缓冲区容量设置为4MB，远超实际需要

### 2. **地址回绕逻辑问题**
在`SPI_FLASH_WriteRecord`函数中：
```c
nextAddr = RingBuffer.CurrentAddr + SPI_FLASH_RECORD_SIZE;
if (nextAddr > SPI_FLASH_RING_BUFFER_END) {
    nextAddr = SPI_FLASH_RING_BUFFER_START;
}
```

当写入超过某个位置时，地址会回绕到起始位置，可能覆盖之前的数据。

### 3. **扇区擦除时机问题**
```c
if ((RingBuffer.CurrentAddr % SPI_FLASH_SECTOR_SIZE) == 0) {
    status = SPI_FLASH_EraseSector(RingBuffer.CurrentAddr);
}
```

这个擦除逻辑在地址回绕时可能会擦除包含有效数据的扇区。

### 4. **读取索引计算问题**
```c
physicalIndex = (RingBuffer.ReadRecords + RecordIndex) % SPI_FLASH_MAX_RECORDS;
```

当发生地址回绕后，物理索引计算可能不正确，导致读取到错误的数据。

## 解决方案## 建议的修改步骤

1. **更新头文件配置**：
   - 将`SPI_FLASH_RING_BUFFER_SIZE`改为3MB（0x300000）
   - 重新计算`SPI_FLASH_MAX_RECORDS`

2. **测试前清空Flash**：
   ```c
   // 在测试前添加完整的芯片擦除
   printf("Erasing entire chip...\r\n");
   if (SPI_FLASH_EraseChip() == HAL_OK) {
       printf("Chip erase: OK\r\n");
   } else {
       printf("Chip erase: FAIL\r\n");
   }
   ```

3. **添加调试信息**：
   - 在关键位置添加地址和状态打印
   - 使用新的`SPI_FLASH_PrintRingBufferStatus()`函数

4. **分阶段测试**：
   - 先测试少量数据（如50条）
   - 确认读写正常后再增加到1000条
   - 观察地址回绕的行为

## 根本原因总结

您遇到的问题主要是：
1. **地址回绕**：当写入超过缓冲区大小时，地址回绕导致覆盖了之前的数据
2. **索引计算错误**：读取时的物理地址计算在回绕情况下不正确
3. **扇区擦除冲突**：擦除时可能删除了包含有效数据的扇区



/**
 * 修复后的SPI Flash环形缓冲区配置和关键函数
 * 适用于ZD25WQ32CEIGR (32Mbit/4MB)芯片
 */

// 修正后的环形缓冲区参数配置
#define SPI_FLASH_TOTAL_SIZE             (4 * 1024 * 1024)  // 4MB总容量
#define SPI_FLASH_RING_BUFFER_START      0x00000
#define SPI_FLASH_RING_BUFFER_SIZE       (3 * 1024 * 1024)  // 使用3MB作为环形缓冲区，留1MB给其他用途
#define SPI_FLASH_RECORD_SIZE            120
#define SPI_FLASH_RING_BUFFER_END        (SPI_FLASH_RING_BUFFER_START + SPI_FLASH_RING_BUFFER_SIZE - 1)
#define SPI_FLASH_MAX_RECORDS            (SPI_FLASH_RING_BUFFER_SIZE / SPI_FLASH_RECORD_SIZE)  // 大约25600条记录

// 修复后的写入记录函数
HAL_StatusTypeDef SPI_FLASH_WriteRecord(uint8_t *pData, uint16_t Size)
{
    HAL_StatusTypeDef status = HAL_OK;
    uint8_t recordBuffer[SPI_FLASH_RECORD_SIZE];
    uint32_t nextAddr;
    uint32_t primask;
    uint32_t currentSector, nextSector;

    if (pData == NULL || Size == 0 || Size > SPI_FLASH_RECORD_SIZE) {
        return HAL_ERROR;
    }

    primask = __get_PRIMASK();
    __disable_irq();

    if (!IsRingBufferInitialized) {
        status = SPI_FLASH_InitRingBuffer();
        if (status != HAL_OK) {
            if (!primask) { __enable_irq(); }
            return status;
        }
    }

    // 准备写入数据
    memset(recordBuffer, 0xFF, SPI_FLASH_RECORD_SIZE);
    memcpy(recordBuffer, pData, Size);

    // 计算下一个地址
    nextAddr = RingBuffer.CurrentAddr + SPI_FLASH_RECORD_SIZE;
    if (nextAddr > SPI_FLASH_RING_BUFFER_END) {
        nextAddr = SPI_FLASH_RING_BUFFER_START;
        printf("Ring buffer wrapped around to start\r\n");
    }

    // 改进的擦除逻辑：检查是否需要擦除扇区
    currentSector = RingBuffer.CurrentAddr / SPI_FLASH_SECTOR_SIZE;
    nextSector = nextAddr / SPI_FLASH_SECTOR_SIZE;

    // 如果当前地址是扇区的开始，或者跨越了扇区边界，则擦除当前扇区
    if ((RingBuffer.CurrentAddr % SPI_FLASH_SECTOR_SIZE) == 0) {
        uint32_t sectorAddr = currentSector * SPI_FLASH_SECTOR_SIZE;
        printf("Erasing sector at address: 0x%08X\r\n", sectorAddr);
        status = SPI_FLASH_EraseSector(sectorAddr);
        if (status != HAL_OK) {
            printf("Sector erase failed at address: 0x%08X\r\n", sectorAddr);
            if (!primask) { __enable_irq(); }
            return status;
        }
        printf("Sector erase OK at address: 0x%08X\r\n", sectorAddr);
    }

    // 写入数据
    status = SPI_FLASH_WriteData(RingBuffer.CurrentAddr, recordBuffer, SPI_FLASH_RECORD_SIZE);
    if (status != HAL_OK) {
        if (!primask) { __enable_irq(); }
        return status;
    }

    // 更新缓冲区状态
    RingBuffer.CurrentAddr = nextAddr;
    RingBuffer.TotalRecords++;

    // 如果超过最大记录数，更新已读记录计数（实现真正的环形缓冲区）
    if (RingBuffer.TotalRecords > SPI_FLASH_MAX_RECORDS) {
        RingBuffer.ReadRecords = RingBuffer.TotalRecords - SPI_FLASH_MAX_RECORDS;
    }

    status = SPI_FLASH_SaveRingBufferInfo();

    if (!primask) { __enable_irq(); }
    return status;
}

// 修复后的读取记录函数
HAL_StatusTypeDef SPI_FLASH_ReadRecordEx(uint32_t RecordIndex, uint8_t *pData, uint16_t *Size, uint8_t MarkAsRead)
{
    HAL_StatusTypeDef status = HAL_OK;
    uint32_t readAddr;
    uint32_t primask;

    if (pData == NULL || Size == NULL || *Size == 0) {
        return HAL_ERROR;
    }

    primask = __get_PRIMASK();
    __disable_irq();

    if (!IsRingBufferInitialized) {
        status = SPI_FLASH_InitRingBuffer();
        if (status != HAL_OK) {
            if (!primask) { __enable_irq(); }
            return status;
        }
    }

    uint32_t unreadRecords = SPI_FLASH_GetRecordCount();
    if (unreadRecords == 0) {
        if (!primask) { __enable_irq(); }
        return HAL_ERROR;
    }

    if (RecordIndex >= unreadRecords) {
        if (!primask) { __enable_irq(); }
        return HAL_ERROR;
    }

    // 改进的物理索引计算
    uint32_t physicalIndex;
    uint32_t totalValidRecords = (RingBuffer.TotalRecords > SPI_FLASH_MAX_RECORDS) ?
                                SPI_FLASH_MAX_RECORDS : RingBuffer.TotalRecords;

    if (RingBuffer.TotalRecords <= SPI_FLASH_MAX_RECORDS) {
        // 未发生回绕的情况
        physicalIndex = RingBuffer.ReadRecords + RecordIndex;
    } else {
        // 发生回绕的情况
        uint32_t startIndex = RingBuffer.TotalRecords - SPI_FLASH_MAX_RECORDS;
        physicalIndex = (startIndex + RingBuffer.ReadRecords - startIndex + RecordIndex) % SPI_FLASH_MAX_RECORDS;
    }

    readAddr = SPI_FLASH_RING_BUFFER_START + (physicalIndex * SPI_FLASH_RECORD_SIZE);

    // 确保读取地址在有效范围内
    if (readAddr > SPI_FLASH_RING_BUFFER_END) {
        if (!primask) { __enable_irq(); }
        return HAL_ERROR;
    }

    status = SPI_FLASH_ReadData(readAddr, pData, SPI_FLASH_RECORD_SIZE);
    if (status != HAL_OK) {
        if (!primask) { __enable_irq(); }
        return status;
    }

    *Size = SPI_FLASH_RECORD_SIZE;

    // 标记为已读
    if (MarkAsRead && RecordIndex == 0 && RingBuffer.ReadRecords < RingBuffer.TotalRecords) {
        RingBuffer.ReadRecords++;
        status = SPI_FLASH_SaveRingBufferInfo();
        if (status != HAL_OK) {
            if (!primask) { __enable_irq(); }
            return status;
        }
    }

    if (!primask) { __enable_irq(); }
    return HAL_OK;
}

// 修复后的获取记录数量函数
uint32_t SPI_FLASH_GetRecordCount(void)
{
    if (!IsRingBufferInitialized) {
        if (SPI_FLASH_InitRingBuffer() != HAL_OK) {
            return 0;
        }
    }

    uint32_t unreadRecords = 0;

    if (RingBuffer.TotalRecords <= SPI_FLASH_MAX_RECORDS) {
        // 未发生回绕
        unreadRecords = RingBuffer.TotalRecords - RingBuffer.ReadRecords;
    } else {
        // 发生回绕，只能读取最新的SPI_FLASH_MAX_RECORDS条记录
        uint32_t availableRecords = SPI_FLASH_MAX_RECORDS;
        uint32_t readFromLatest = RingBuffer.ReadRecords - (RingBuffer.TotalRecords - SPI_FLASH_MAX_RECORDS);
        unreadRecords = availableRecords - readFromLatest;
    }

    return unreadRecords;
}

// 新增：获取环形缓冲区状态信息的调试函数
void SPI_FLASH_PrintRingBufferStatus(void)
{
    printf("=== Ring Buffer Status ===\r\n");
    printf("Current Address: 0x%08X\r\n", RingBuffer.CurrentAddr);
    printf("Total Records: %lu\r\n", RingBuffer.TotalRecords);
    printf("Read Records: %lu\r\n", RingBuffer.ReadRecords);
    printf("Unread Records: %lu\r\n", SPI_FLASH_GetRecordCount());
    printf("Max Capacity: %lu records\r\n", SPI_FLASH_MAX_RECORDS);
    printf("Buffer Size: %lu bytes\r\n", SPI_FLASH_RING_BUFFER_SIZE);
    printf("Record Size: %d bytes\r\n", SPI_FLASH_RECORD_SIZE);

    if (RingBuffer.TotalRecords > SPI_FLASH_MAX_RECORDS) {
        printf("WARNING: Ring buffer has wrapped around!\r\n");
    }
    printf("========================\r\n");
}
